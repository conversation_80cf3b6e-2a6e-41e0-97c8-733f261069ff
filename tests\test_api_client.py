"""
Test cases for api_client module
"""

import pytest
import asyncio
from unittest.mock import AsyncMock, patch, MagicMock
from src.api_client import WaybackAPIClient, check_wayback_snapshots


class TestWaybackAPIClient:
    """Test cases for WaybackAPIClient class."""
    
    def setup_method(self):
        """Setup test fixtures."""
        self.client = WaybackAPIClient()
        
    @pytest.mark.asyncio
    async def test_build_api_url(self):
        """Test API URL building."""
        url = self.client._build_api_url("example.com")
        
        assert "web.archive.org/cdx/search/cdx" in url
        assert "url=example.com" in url
        assert "output=json" in url
        assert "fl=timestamp,original,statuscode" in url
        
    @pytest.mark.asyncio
    async def test_rate_limit(self):
        """Test rate limiting functionality."""
        import time
        
        start_time = time.time()
        await self.client._rate_limit()
        await self.client._rate_limit()
        end_time = time.time()
        
        # Should have some delay between calls
        assert end_time - start_time >= 0.1  # At least some delay
        
    @pytest.mark.asyncio
    async def test_fetch_single_url_success(self):
        """Test successful single URL fetch."""
        # Mock the aiohttp session
        mock_response = AsyncMock()
        mock_response.status = 200
        mock_response.text.return_value = '[["timestamp","original","statuscode"],["20230101000000","http://example.com","200"]]'
        
        mock_session = AsyncMock()
        mock_session.get.return_value.__aenter__.return_value = mock_response
        
        self.client.session = mock_session
        
        result = await self.client._fetch_single_url("example.com")
        
        assert result[0] == "example.com"
        assert result[1] == 1  # One snapshot (excluding header)
        assert result[2] == "Success"
        
    @pytest.mark.asyncio
    async def test_fetch_single_url_empty_response(self):
        """Test handling of empty response."""
        mock_response = AsyncMock()
        mock_response.status = 200
        mock_response.text.return_value = ""
        
        mock_session = AsyncMock()
        mock_session.get.return_value.__aenter__.return_value = mock_response
        
        self.client.session = mock_session
        
        result = await self.client._fetch_single_url("example.com")
        
        assert result[0] == "example.com"
        assert result[1] == 0
        assert result[2] == "Success"
        
    @pytest.mark.asyncio
    async def test_fetch_single_url_404(self):
        """Test handling of 404 response."""
        mock_response = AsyncMock()
        mock_response.status = 404
        
        mock_session = AsyncMock()
        mock_session.get.return_value.__aenter__.return_value = mock_response
        
        self.client.session = mock_session
        
        result = await self.client._fetch_single_url("example.com")
        
        assert result[0] == "example.com"
        assert result[1] == 0
        assert result[2] == "Success"  # 404 is treated as success (no snapshots)
        
    @pytest.mark.asyncio
    async def test_fetch_single_url_timeout(self):
        """Test handling of timeout."""
        mock_session = AsyncMock()
        mock_session.get.side_effect = asyncio.TimeoutError()
        
        self.client.session = mock_session
        
        result = await self.client._fetch_single_url("example.com")
        
        assert result[0] == "example.com"
        assert result[1] == 0
        assert result[2] == "Timeout"
        
    @pytest.mark.asyncio
    async def test_fetch_single_url_invalid_json(self):
        """Test handling of invalid JSON response."""
        mock_response = AsyncMock()
        mock_response.status = 200
        mock_response.text.return_value = "invalid json"
        
        mock_session = AsyncMock()
        mock_session.get.return_value.__aenter__.return_value = mock_response
        
        self.client.session = mock_session
        
        result = await self.client._fetch_single_url("example.com")
        
        assert result[0] == "example.com"
        assert result[1] == 0
        assert result[2] == "Invalid JSON Response"
        
    @pytest.mark.asyncio
    async def test_check_snapshots_batch(self):
        """Test batch processing of URLs."""
        urls = ["example.com", "google.com", "facebook.com"]
        
        # Mock the _fetch_single_url method
        async def mock_fetch(url):
            return (url, 100, "Success")
            
        self.client._fetch_single_url = mock_fetch
        
        results = await self.client.check_snapshots_batch(urls)
        
        assert len(results) == 3
        assert all(result[2] == "Success" for result in results)
        assert all(result[1] == 100 for result in results)
        
    @pytest.mark.asyncio
    async def test_check_snapshots_batch_with_progress(self):
        """Test batch processing with progress callback."""
        urls = ["example.com", "google.com"]
        progress_calls = []
        
        def progress_callback(percentage, current, total):
            progress_calls.append((percentage, current, total))
            
        # Mock the _fetch_single_url method
        async def mock_fetch(url):
            return (url, 100, "Success")
            
        self.client._fetch_single_url = mock_fetch
        
        results = await self.client.check_snapshots_batch(urls, progress_callback)
        
        assert len(results) == 2
        assert len(progress_calls) > 0  # Progress callback was called
        
    @pytest.mark.asyncio
    async def test_check_snapshots_batch_empty_list(self):
        """Test batch processing with empty URL list."""
        results = await self.client.check_snapshots_batch([])
        assert results == []
        
    @pytest.mark.asyncio
    async def test_context_manager(self):
        """Test async context manager functionality."""
        async with WaybackAPIClient() as client:
            assert client.session is not None
            
        # Session should be closed after exiting context
        assert client.session is None or client.session.closed


@pytest.mark.asyncio
async def test_check_wayback_snapshots_convenience_function():
    """Test the convenience function."""
    urls = ["example.com"]
    
    with patch('src.api_client.WaybackAPIClient') as mock_client_class:
        mock_client = AsyncMock()
        mock_client.check_snapshots_batch.return_value = [("example.com", 100, "Success")]
        mock_client_class.return_value.__aenter__.return_value = mock_client
        
        results = await check_wayback_snapshots(urls)
        
        assert len(results) == 1
        assert results[0] == ("example.com", 100, "Success")
