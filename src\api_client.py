"""
Wayback Machine API Client Module

Handles asynchronous API calls to Wayback Machine CDX API with proper error handling,
rate limiting, and timeout management.
"""

import asyncio
import aiohttp
import json
import time
from typing import Dict, List, Optional, Tuple
from urllib.parse import urlparse
import logging

from config import (
    WAYBACK_API_BASE_URL, 
    API_TIMEOUT, 
    REQUEST_DELAY, 
    BATCH_SIZE
)


class WaybackAPIClient:
    """
    Asynchronous client for Wayback Machine CDX API.
    
    Provides methods to check snapshot counts for URLs with proper error handling,
    rate limiting, and concurrent processing capabilities.
    """
    
    def __init__(self):
        """Initialize the API client with default settings."""
        self.session: Optional[aiohttp.ClientSession] = None
        self.last_request_time = 0
        self.logger = logging.getLogger(__name__)
        
    async def __aenter__(self):
        """Async context manager entry."""
        await self.start_session()
        return self
        
    async def __aexit__(self, exc_type, exc_val, exc_tb):
        """Async context manager exit."""
        await self.close_session()
        
    async def start_session(self):
        """Start the aiohttp session."""
        if self.session is None:
            timeout = aiohttp.ClientTimeout(total=API_TIMEOUT)
            self.session = aiohttp.ClientSession(timeout=timeout)
            
    async def close_session(self):
        """Close the aiohttp session."""
        if self.session:
            await self.session.close()
            self.session = None
            
    async def _rate_limit(self):
        """Implement rate limiting between requests."""
        current_time = time.time()
        time_since_last = current_time - self.last_request_time
        
        if time_since_last < REQUEST_DELAY:
            await asyncio.sleep(REQUEST_DELAY - time_since_last)
            
        self.last_request_time = time.time()
        
    def _build_api_url(self, url: str) -> str:
        """
        Build the complete API URL for a given domain/URL.
        
        Args:
            url: The URL to check snapshots for
            
        Returns:
            Complete API URL string
        """
        params = {
            'url': url,
            'output': 'json',
            'fl': 'timestamp,original,statuscode'
        }
        
        param_string = '&'.join([f"{k}={v}" for k, v in params.items()])
        return f"{WAYBACK_API_BASE_URL}?{param_string}"
        
    async def _fetch_single_url(self, url: str) -> Tuple[str, int, str]:
        """
        Fetch snapshot count for a single URL.
        
        Args:
            url: The URL to check
            
        Returns:
            Tuple of (url, snapshot_count, status)
        """
        if not self.session:
            await self.start_session()
            
        await self._rate_limit()
        
        try:
            api_url = self._build_api_url(url)
            
            async with self.session.get(api_url) as response:
                if response.status == 200:
                    text_content = await response.text()
                    
                    if not text_content.strip():
                        return url, 0, "Success"
                        
                    try:
                        # Parse JSON response
                        json_data = json.loads(text_content)
                        
                        # Count snapshots (exclude header row if present)
                        if isinstance(json_data, list):
                            # First row might be headers, check if it contains field names
                            snapshot_count = len(json_data)
                            if (snapshot_count > 0 and 
                                isinstance(json_data[0], list) and 
                                len(json_data[0]) > 0 and 
                                json_data[0][0] == 'timestamp'):
                                snapshot_count -= 1  # Exclude header row
                                
                            return url, max(0, snapshot_count), "Success"
                        else:
                            return url, 0, "Invalid Response Format"
                            
                    except json.JSONDecodeError:
                        self.logger.warning(f"Invalid JSON response for {url}")
                        return url, 0, "Invalid JSON Response"
                        
                elif response.status == 404:
                    return url, 0, "Success"  # No snapshots found is valid
                else:
                    return url, 0, f"HTTP {response.status}"
                    
        except asyncio.TimeoutError:
            self.logger.warning(f"Timeout for {url}")
            return url, 0, "Timeout"
        except aiohttp.ClientError as e:
            self.logger.error(f"Client error for {url}: {e}")
            return url, 0, "Network Error"
        except Exception as e:
            self.logger.error(f"Unexpected error for {url}: {e}")
            return url, 0, "Error"
            
    async def check_snapshots_batch(self, urls: List[str], 
                                  progress_callback=None) -> List[Tuple[str, int, str]]:
        """
        Check snapshot counts for a batch of URLs concurrently.
        
        Args:
            urls: List of URLs to check
            progress_callback: Optional callback function for progress updates
            
        Returns:
            List of tuples (url, snapshot_count, status)
        """
        if not urls:
            return []
            
        results = []
        total_urls = len(urls)
        
        # Process URLs in batches to avoid overwhelming the API
        for i in range(0, total_urls, BATCH_SIZE):
            batch = urls[i:i + BATCH_SIZE]
            
            # Create tasks for concurrent execution
            tasks = [self._fetch_single_url(url) for url in batch]
            
            # Execute batch concurrently
            batch_results = await asyncio.gather(*tasks, return_exceptions=True)
            
            # Process results and handle exceptions
            for j, result in enumerate(batch_results):
                if isinstance(result, Exception):
                    url = batch[j]
                    self.logger.error(f"Exception for {url}: {result}")
                    results.append((url, 0, "Error"))
                else:
                    results.append(result)
                    
            # Update progress if callback provided
            if progress_callback:
                progress = min(100, int((i + len(batch)) / total_urls * 100))
                progress_callback(progress, i + len(batch), total_urls)
                
        return results
        
    async def check_single_url(self, url: str) -> Tuple[str, int, str]:
        """
        Check snapshot count for a single URL.
        
        Args:
            url: The URL to check
            
        Returns:
            Tuple of (url, snapshot_count, status)
        """
        return await self._fetch_single_url(url)


# Convenience function for simple usage
async def check_wayback_snapshots(urls: List[str], 
                                progress_callback=None) -> List[Tuple[str, int, str]]:
    """
    Convenience function to check Wayback Machine snapshots for multiple URLs.
    
    Args:
        urls: List of URLs to check
        progress_callback: Optional callback for progress updates
        
    Returns:
        List of tuples (url, snapshot_count, status)
    """
    async with WaybackAPIClient() as client:
        return await client.check_snapshots_batch(urls, progress_callback)
