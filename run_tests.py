"""
Test runner script for Wayback Machine Snapshot Checker

This script runs all tests and provides a comprehensive test report.
"""

import sys
import subprocess
import os
from pathlib import Path


def run_command(command, description):
    """
    Run a command and return the result.
    
    Args:
        command: Command to run
        description: Description of what the command does
        
    Returns:
        Tuple of (success, output)
    """
    print(f"\n{'='*50}")
    print(f"Running: {description}")
    print(f"Command: {command}")
    print(f"{'='*50}")
    
    try:
        result = subprocess.run(
            command, 
            shell=True, 
            capture_output=True, 
            text=True,
            timeout=300  # 5 minute timeout
        )
        
        print(result.stdout)
        if result.stderr:
            print("STDERR:", result.stderr)
            
        success = result.returncode == 0
        print(f"Result: {'SUCCESS' if success else 'FAILED'}")
        
        return success, result.stdout
        
    except subprocess.TimeoutExpired:
        print("TIMEOUT: Command took too long to execute")
        return False, ""
    except Exception as e:
        print(f"ERROR: {e}")
        return False, ""


def check_dependencies():
    """Check if required dependencies are installed."""
    print("Checking dependencies...")
    
    required_packages = [
        'PyQt6',
        'aiohttp',
        'validators',
        'pytest',
        'pytest-asyncio'
    ]
    
    missing_packages = []
    
    for package in required_packages:
        try:
            __import__(package.lower().replace('-', '_'))
            print(f"✓ {package}")
        except ImportError:
            print(f"✗ {package} (missing)")
            missing_packages.append(package)
    
    if missing_packages:
        print(f"\nMissing packages: {', '.join(missing_packages)}")
        print("Install them with: pip install " + " ".join(missing_packages))
        return False
    
    return True


def run_unit_tests():
    """Run unit tests."""
    if not os.path.exists("tests"):
        print("No tests directory found. Skipping unit tests.")
        return True
        
    success, output = run_command(
        "python -m pytest tests/ -v --tb=short",
        "Unit Tests"
    )
    
    return success


def run_code_quality_checks():
    """Run code quality checks."""
    checks = [
        ("python -m flake8 src/ --max-line-length=100 --ignore=E203,W503", "Code Style Check (flake8)"),
        ("python -c \"import src.api_client; print('✓ API Client import successful')\"", "Import Test - API Client"),
        ("python -c \"import src.data_processor; print('✓ Data Processor import successful')\"", "Import Test - Data Processor"),
        ("python -c \"import src.ui_components; print('✓ UI Components import successful')\"", "Import Test - UI Components"),
    ]
    
    all_success = True
    
    for command, description in checks:
        success, _ = run_command(command, description)
        if not success:
            all_success = False
            
    return all_success


def run_integration_test():
    """Run a basic integration test."""
    test_script = """
import asyncio
import sys
sys.path.append('.')

from src.data_processor import DataManager
from src.api_client import WaybackAPIClient

async def test_integration():
    # Test data processing
    manager = DataManager()
    valid_urls, invalid_urls = manager.process_input("example.com\\ninvalid-url")
    
    assert len(valid_urls) == 1
    assert len(invalid_urls) == 1
    print("✓ Data processing test passed")
    
    # Test API client initialization
    async with WaybackAPIClient() as client:
        assert client is not None
        print("✓ API client initialization test passed")
    
    print("✓ Integration test completed successfully")

if __name__ == "__main__":
    asyncio.run(test_integration())
"""
    
    # Write test script to temporary file
    with open("temp_integration_test.py", "w") as f:
        f.write(test_script)
    
    try:
        success, output = run_command(
            "python temp_integration_test.py",
            "Integration Test"
        )
        return success
    finally:
        # Clean up temporary file
        if os.path.exists("temp_integration_test.py"):
            os.remove("temp_integration_test.py")


def main():
    """Main test runner function."""
    print("Wayback Machine Snapshot Checker - Test Suite")
    print("=" * 60)
    
    # Check if we're in the right directory
    if not os.path.exists("main.py"):
        print("ERROR: Please run this script from the project root directory")
        sys.exit(1)
    
    # Track test results
    test_results = {}
    
    # Check dependencies
    print("\n1. Checking Dependencies...")
    test_results["dependencies"] = check_dependencies()
    
    if not test_results["dependencies"]:
        print("\nERROR: Missing dependencies. Please install them first.")
        sys.exit(1)
    
    # Run unit tests
    print("\n2. Running Unit Tests...")
    test_results["unit_tests"] = run_unit_tests()
    
    # Run code quality checks
    print("\n3. Running Code Quality Checks...")
    test_results["code_quality"] = run_code_quality_checks()
    
    # Run integration test
    print("\n4. Running Integration Test...")
    test_results["integration"] = run_integration_test()
    
    # Summary
    print("\n" + "=" * 60)
    print("TEST SUMMARY")
    print("=" * 60)
    
    for test_name, result in test_results.items():
        status = "PASS" if result else "FAIL"
        print(f"{test_name.replace('_', ' ').title()}: {status}")
    
    all_passed = all(test_results.values())
    
    if all_passed:
        print("\n🎉 All tests passed! The application is ready to use.")
        print("\nTo run the application:")
        print("python main.py")
    else:
        print("\n❌ Some tests failed. Please check the output above.")
        sys.exit(1)


if __name__ == "__main__":
    main()
