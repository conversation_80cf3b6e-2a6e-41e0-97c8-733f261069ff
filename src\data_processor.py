"""
Data Processing Module for Wayback Machine Snapshot Checker

Handles URL validation, normalization, data processing, and export functionality.
"""

import re
import csv
import io
from typing import List, Tuple, Dict, Optional
from urllib.parse import urlparse, urlunparse
import validators
import logging

from config import MAX_URLS, EXPORT_FORMATS, DEFAULT_EXPORT_FORMAT


class URLProcessor:
    """
    Handles URL validation, normalization and processing operations.
    """
    
    def __init__(self):
        """Initialize the URL processor."""
        self.logger = logging.getLogger(__name__)
        
    def normalize_url(self, url: str) -> str:
        """
        Normalize URL to a standard format for API calls.
        
        Args:
            url: Raw URL string
            
        Returns:
            Normalized URL string
        """
        url = url.strip()
        
        if not url:
            return ""
            
        # Remove common prefixes that might interfere
        url = re.sub(r'^https?://', '', url)
        url = re.sub(r'^www\.', '', url)
        
        # Remove trailing slashes and paths for domain-level checking
        url = url.split('/')[0]
        
        # Remove port numbers if present
        url = url.split(':')[0]
        
        return url.lower()
        
    def validate_url(self, url: str) -> bool:
        """
        Validate if URL is in acceptable format.
        
        Args:
            url: URL string to validate
            
        Returns:
            True if URL is valid, False otherwise
        """
        if not url or len(url.strip()) == 0:
            return False
            
        normalized = self.normalize_url(url)
        
        # Basic domain format check
        domain_pattern = r'^[a-zA-Z0-9]([a-zA-Z0-9\-]{0,61}[a-zA-Z0-9])?(\.[a-zA-Z0-9]([a-zA-Z0-9\-]{0,61}[a-zA-Z0-9])?)*$'
        
        if not re.match(domain_pattern, normalized):
            return False
            
        # Check if it looks like a valid domain
        if '.' not in normalized:
            return False
            
        # Additional validation using validators library
        test_url = f"http://{normalized}"
        return validators.url(test_url)
        
    def process_url_list(self, raw_input: str) -> Tuple[List[str], List[str]]:
        """
        Process raw URL input and return valid and invalid URLs.
        
        Args:
            raw_input: Raw text input containing URLs
            
        Returns:
            Tuple of (valid_urls, invalid_urls)
        """
        if not raw_input:
            return [], []
            
        # Split by lines and clean up
        lines = [line.strip() for line in raw_input.split('\n')]
        lines = [line for line in lines if line]  # Remove empty lines
        
        valid_urls = []
        invalid_urls = []
        
        for line in lines:
            if len(valid_urls) >= MAX_URLS:
                self.logger.warning(f"Maximum URL limit ({MAX_URLS}) reached. Ignoring remaining URLs.")
                break
                
            if self.validate_url(line):
                normalized = self.normalize_url(line)
                if normalized not in valid_urls:  # Avoid duplicates
                    valid_urls.append(normalized)
            else:
                invalid_urls.append(line)
                
        return valid_urls, invalid_urls


class ResultProcessor:
    """
    Handles processing and formatting of results from API calls.
    """
    
    def __init__(self):
        """Initialize the result processor."""
        self.logger = logging.getLogger(__name__)
        
    def format_results_for_display(self, results: List[Tuple[str, int, str]]) -> List[Dict[str, str]]:
        """
        Format API results for display in UI table.
        
        Args:
            results: List of (url, snapshot_count, status) tuples
            
        Returns:
            List of dictionaries with formatted data
        """
        formatted_results = []
        
        for url, count, status in results:
            formatted_results.append({
                'URL': url,
                'Snapshots': str(count),
                'Status': status
            })
            
        return formatted_results
        
    def generate_summary_stats(self, results: List[Tuple[str, int, str]]) -> Dict[str, int]:
        """
        Generate summary statistics from results.
        
        Args:
            results: List of (url, snapshot_count, status) tuples
            
        Returns:
            Dictionary with summary statistics
        """
        total_urls = len(results)
        successful = sum(1 for _, _, status in results if status == "Success")
        errors = total_urls - successful
        total_snapshots = sum(count for _, count, status in results if status == "Success")
        
        return {
            'total_urls': total_urls,
            'successful': successful,
            'errors': errors,
            'total_snapshots': total_snapshots
        }
        
    def export_to_tsv(self, results: List[Tuple[str, int, str]]) -> str:
        """
        Export results to TSV format.
        
        Args:
            results: List of (url, snapshot_count, status) tuples
            
        Returns:
            TSV formatted string
        """
        output = io.StringIO()
        
        # Write header
        output.write("URL\tSnapshots\tStatus\n")
        
        # Write data
        for url, count, status in results:
            output.write(f"{url}\t{count}\t{status}\n")
            
        return output.getvalue()
        
    def export_to_csv(self, results: List[Tuple[str, int, str]]) -> str:
        """
        Export results to CSV format.
        
        Args:
            results: List of (url, snapshot_count, status) tuples
            
        Returns:
            CSV formatted string
        """
        output = io.StringIO()
        writer = csv.writer(output)
        
        # Write header
        writer.writerow(["URL", "Snapshots", "Status"])
        
        # Write data
        for url, count, status in results:
            writer.writerow([url, count, status])
            
        return output.getvalue()
        
    def export_results(self, results: List[Tuple[str, int, str]], 
                      format_type: str = DEFAULT_EXPORT_FORMAT) -> str:
        """
        Export results in specified format.
        
        Args:
            results: List of (url, snapshot_count, status) tuples
            format_type: Export format ("TSV" or "CSV")
            
        Returns:
            Formatted string ready for export
        """
        format_type = format_type.upper()
        
        if format_type == "TSV":
            return self.export_to_tsv(results)
        elif format_type == "CSV":
            return self.export_to_csv(results)
        else:
            self.logger.warning(f"Unknown export format: {format_type}. Using TSV.")
            return self.export_to_tsv(results)


class DataManager:
    """
    Main data management class that combines URL processing and result processing.
    """
    
    def __init__(self):
        """Initialize the data manager."""
        self.url_processor = URLProcessor()
        self.result_processor = ResultProcessor()
        self.logger = logging.getLogger(__name__)
        
    def process_input(self, raw_input: str) -> Tuple[List[str], List[str]]:
        """
        Process raw URL input.
        
        Args:
            raw_input: Raw text input containing URLs
            
        Returns:
            Tuple of (valid_urls, invalid_urls)
        """
        return self.url_processor.process_url_list(raw_input)
        
    def format_results(self, results: List[Tuple[str, int, str]]) -> List[Dict[str, str]]:
        """
        Format results for display.
        
        Args:
            results: API results
            
        Returns:
            Formatted results for UI display
        """
        return self.result_processor.format_results_for_display(results)
        
    def get_summary(self, results: List[Tuple[str, int, str]]) -> Dict[str, int]:
        """
        Get summary statistics.
        
        Args:
            results: API results
            
        Returns:
            Summary statistics
        """
        return self.result_processor.generate_summary_stats(results)
        
    def export_data(self, results: List[Tuple[str, int, str]], 
                   format_type: str = DEFAULT_EXPORT_FORMAT) -> str:
        """
        Export results in specified format.
        
        Args:
            results: API results
            format_type: Export format
            
        Returns:
            Formatted export string
        """
        return self.result_processor.export_results(results, format_type)
