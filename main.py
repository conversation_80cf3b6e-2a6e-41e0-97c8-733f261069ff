"""
Main Application Controller for Wayback Machine Snapshot Checker

Entry point and main controller that coordinates all modules and handles
the application lifecycle.
"""

import sys
import asyncio
import logging
from typing import List, <PERSON><PERSON>, Optional
from PyQt6.QtWidgets import QApplication
from PyQt6.QtCore import QThread, pyqtSignal, QObject
from PyQt6.QtGui import QIcon

# Import our modules
from src.ui_components import MainWindow
from src.data_processor import DataManager
from src.api_client import check_wayback_snapshots
from config import WINDOW_TITLE


class WorkerSignals(QObject):
    """
    Signals for the worker thread to communicate with the main thread.
    """
    finished = pyqtSignal(list)  # Emitted when work is complete
    error = pyqtSignal(str)      # Emitted when an error occurs
    progress = pyqtSignal(int, int, int)  # Emitted for progress updates


class SnapshotWorker(QThread):
    """
    Worker thread for handling API calls without blocking the UI.
    """
    
    def __init__(self, urls: List[str]):
        """
        Initialize the worker thread.
        
        Args:
            urls: List of URLs to check
        """
        super().__init__()
        self.urls = urls
        self.signals = WorkerSignals()
        self.logger = logging.getLogger(__name__)
        
    def run(self):
        """Run the worker thread."""
        try:
            # Create new event loop for this thread
            loop = asyncio.new_event_loop()
            asyncio.set_event_loop(loop)
            
            # Define progress callback
            def progress_callback(percentage: int, current: int, total: int):
                self.signals.progress.emit(percentage, current, total)
            
            # Run the async function
            results = loop.run_until_complete(
                check_wayback_snapshots(self.urls, progress_callback)
            )
            
            # Emit results
            self.signals.finished.emit(results)
            
        except Exception as e:
            self.logger.error(f"Worker thread error: {e}")
            self.signals.error.emit(str(e))
        finally:
            # Clean up the event loop
            loop.close()


class WaybackSnapshotChecker:
    """
    Main application controller that coordinates all components.
    """
    
    def __init__(self):
        """Initialize the application controller."""
        self.setup_logging()
        self.data_manager = DataManager()
        self.current_results = []
        self.worker_thread: Optional[SnapshotWorker] = None
        
        # Initialize UI
        self.app = QApplication(sys.argv)
        self.main_window = MainWindow()
        
        # Connect signals
        self.connect_signals()
        
        self.logger = logging.getLogger(__name__)
        self.logger.info("Application initialized")
        
    def setup_logging(self):
        """Setup logging configuration."""
        logging.basicConfig(
            level=logging.INFO,
            format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
            handlers=[
                logging.FileHandler('wayback_checker.log'),
                logging.StreamHandler()
            ]
        )
        
    def connect_signals(self):
        """Connect UI signals to their handlers."""
        # Input section signals
        self.main_window.input_section.check_button.clicked.connect(self.start_checking)
        self.main_window.input_section.clear_button.clicked.connect(self.clear_all)
        self.main_window.input_section.export_button.clicked.connect(self.export_results)
        
    def start_checking(self):
        """Start the snapshot checking process."""
        # Get input text
        raw_input = self.main_window.input_section.get_input_text()
        
        if not raw_input.strip():
            self.main_window.show_message(
                "Lỗi", 
                "Vui lòng nhập ít nhất một URL để kiểm tra.",
                "warning"
            )
            return
            
        # Process URLs
        valid_urls, invalid_urls = self.data_manager.process_input(raw_input)
        
        if not valid_urls:
            self.main_window.show_message(
                "Lỗi",
                "Không có URL hợp lệ nào được tìm thấy. Vui lòng kiểm tra lại định dạng URL.",
                "warning"
            )
            return
            
        # Show warning for invalid URLs if any
        if invalid_urls:
            invalid_count = len(invalid_urls)
            self.main_window.show_message(
                "Cảnh báo",
                f"Tìm thấy {invalid_count} URL không hợp lệ sẽ bị bỏ qua.\n"
                f"Tiếp tục với {len(valid_urls)} URL hợp lệ?",
                "warning"
            )
            
        # Update UI for processing state
        self.main_window.input_section.set_buttons_enabled(False)
        self.main_window.results_section.show_progress(True)
        self.main_window.results_section.clear_results()
        self.main_window.status_section.clear_summary()
        self.main_window.status_bar.showMessage("Đang kiểm tra snapshots...")
        
        # Start worker thread
        self.worker_thread = SnapshotWorker(valid_urls)
        self.worker_thread.signals.finished.connect(self.on_checking_finished)
        self.worker_thread.signals.error.connect(self.on_checking_error)
        self.worker_thread.signals.progress.connect(self.on_progress_update)
        self.worker_thread.start()
        
        self.logger.info(f"Started checking {len(valid_urls)} URLs")
        
    def on_progress_update(self, percentage: int, current: int, total: int):
        """
        Handle progress updates from worker thread.
        
        Args:
            percentage: Progress percentage
            current: Current item number
            total: Total items
        """
        self.main_window.results_section.update_progress(percentage, current, total)
        
    def on_checking_finished(self, results: List[Tuple[str, int, str]]):
        """
        Handle completion of snapshot checking.
        
        Args:
            results: List of (url, snapshot_count, status) tuples
        """
        self.current_results = results
        
        # Format and display results
        formatted_results = self.data_manager.format_results(results)
        self.main_window.results_section.update_results(formatted_results)
        
        # Update summary
        summary = self.data_manager.get_summary(results)
        self.main_window.status_section.update_summary(summary)
        
        # Update UI state
        self.main_window.results_section.show_progress(False)
        self.main_window.input_section.set_buttons_enabled(True)
        self.main_window.input_section.export_button.setEnabled(True)
        
        # Update status
        success_count = summary.get('successful', 0)
        total_count = summary.get('total_urls', 0)
        self.main_window.status_bar.showMessage(
            f"Hoàn thành! Kiểm tra {total_count} URL, {success_count} thành công"
        )
        
        self.logger.info(f"Checking completed: {total_count} URLs processed, {success_count} successful")
        
    def on_checking_error(self, error_message: str):
        """
        Handle errors from worker thread.
        
        Args:
            error_message: Error message
        """
        self.main_window.show_message(
            "Lỗi",
            f"Đã xảy ra lỗi trong quá trình kiểm tra:\n{error_message}",
            "error"
        )
        
        # Reset UI state
        self.main_window.results_section.show_progress(False)
        self.main_window.input_section.set_buttons_enabled(True)
        self.main_window.status_bar.showMessage("Lỗi trong quá trình kiểm tra")
        
        self.logger.error(f"Checking error: {error_message}")
        
    def clear_all(self):
        """Clear all input and results."""
        self.main_window.input_section.clear_input()
        self.main_window.results_section.clear_results()
        self.main_window.status_section.clear_summary()
        self.main_window.input_section.export_button.setEnabled(False)
        self.main_window.status_bar.showMessage("Đã xóa tất cả dữ liệu")
        self.current_results = []
        
        self.logger.info("Cleared all data")
        
    def export_results(self):
        """Export current results to clipboard."""
        if not self.current_results:
            self.main_window.show_message(
                "Thông báo",
                "Không có dữ liệu để xuất. Vui lòng kiểm tra snapshots trước.",
                "info"
            )
            return
            
        # Export as TSV format
        export_data = self.data_manager.export_data(self.current_results, "TSV")
        self.main_window.copy_to_clipboard(export_data)
        
        self.logger.info(f"Exported {len(self.current_results)} results to clipboard")
        
    def run(self):
        """Run the application."""
        self.main_window.show()
        return self.app.exec()
        
    def cleanup(self):
        """Cleanup resources before exit."""
        if self.worker_thread and self.worker_thread.isRunning():
            self.worker_thread.quit()
            self.worker_thread.wait()
            
        self.logger.info("Application cleanup completed")


def main():
    """Main entry point."""
    try:
        app = WaybackSnapshotChecker()
        exit_code = app.run()
        app.cleanup()
        sys.exit(exit_code)
    except Exception as e:
        logging.error(f"Application startup error: {e}")
        sys.exit(1)


if __name__ == "__main__":
    main()
