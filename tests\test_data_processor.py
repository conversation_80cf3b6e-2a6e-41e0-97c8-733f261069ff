"""
Test cases for data_processor module
"""

import pytest
from src.data_processor import URLProcessor, ResultProcessor, DataManager


class TestURLProcessor:
    """Test cases for URLProcessor class."""
    
    def setup_method(self):
        """Setup test fixtures."""
        self.processor = URLProcessor()
        
    def test_normalize_url_basic(self):
        """Test basic URL normalization."""
        assert self.processor.normalize_url("example.com") == "example.com"
        assert self.processor.normalize_url("www.example.com") == "example.com"
        assert self.processor.normalize_url("https://example.com") == "example.com"
        assert self.processor.normalize_url("http://www.example.com") == "example.com"
        
    def test_normalize_url_with_path(self):
        """Test URL normalization with paths."""
        assert self.processor.normalize_url("example.com/path") == "example.com"
        assert self.processor.normalize_url("https://example.com/path/to/page") == "example.com"
        
    def test_normalize_url_with_port(self):
        """Test URL normalization with ports."""
        assert self.processor.normalize_url("example.com:8080") == "example.com"
        assert self.processor.normalize_url("https://example.com:443/path") == "example.com"
        
    def test_validate_url_valid(self):
        """Test URL validation with valid URLs."""
        valid_urls = [
            "example.com",
            "google.com",
            "sub.domain.com",
            "test-site.org",
            "site123.net"
        ]
        
        for url in valid_urls:
            assert self.processor.validate_url(url), f"URL should be valid: {url}"
            
    def test_validate_url_invalid(self):
        """Test URL validation with invalid URLs."""
        invalid_urls = [
            "",
            "   ",
            "not-a-url",
            "http://",
            "just-text",
            "123.456.789.999",  # Invalid IP
            ".com",
            "example.",
            "-example.com",
            "example-.com"
        ]
        
        for url in invalid_urls:
            assert not self.processor.validate_url(url), f"URL should be invalid: {url}"
            
    def test_process_url_list(self):
        """Test processing of URL lists."""
        input_text = """
        example.com
        https://google.com
        www.facebook.com
        invalid-url
        github.io
        
        duplicate.com
        duplicate.com
        """
        
        valid_urls, invalid_urls = self.processor.process_url_list(input_text)
        
        assert "example.com" in valid_urls
        assert "google.com" in valid_urls
        assert "facebook.com" in valid_urls
        assert "github.io" in valid_urls
        assert "duplicate.com" in valid_urls
        
        # Check for duplicates removal
        assert valid_urls.count("duplicate.com") == 1
        
        assert "invalid-url" in invalid_urls


class TestResultProcessor:
    """Test cases for ResultProcessor class."""
    
    def setup_method(self):
        """Setup test fixtures."""
        self.processor = ResultProcessor()
        self.sample_results = [
            ("example.com", 100, "Success"),
            ("google.com", 500, "Success"),
            ("error-site.com", 0, "Error"),
            ("timeout-site.com", 0, "Timeout")
        ]
        
    def test_format_results_for_display(self):
        """Test formatting results for display."""
        formatted = self.processor.format_results_for_display(self.sample_results)
        
        assert len(formatted) == 4
        assert formatted[0]['URL'] == "example.com"
        assert formatted[0]['Snapshots'] == "100"
        assert formatted[0]['Status'] == "Success"
        
    def test_generate_summary_stats(self):
        """Test summary statistics generation."""
        summary = self.processor.generate_summary_stats(self.sample_results)
        
        assert summary['total_urls'] == 4
        assert summary['successful'] == 2
        assert summary['errors'] == 2
        assert summary['total_snapshots'] == 600
        
    def test_export_to_tsv(self):
        """Test TSV export functionality."""
        tsv_output = self.processor.export_to_tsv(self.sample_results)
        
        lines = tsv_output.strip().split('\n')
        assert len(lines) == 5  # Header + 4 data rows
        assert lines[0] == "URL\tSnapshots\tStatus"
        assert "example.com\t100\tSuccess" in tsv_output
        
    def test_export_to_csv(self):
        """Test CSV export functionality."""
        csv_output = self.processor.export_to_csv(self.sample_results)
        
        lines = csv_output.strip().split('\n')
        assert len(lines) == 5  # Header + 4 data rows
        assert "URL,Snapshots,Status" in csv_output
        assert "example.com,100,Success" in csv_output


class TestDataManager:
    """Test cases for DataManager class."""
    
    def setup_method(self):
        """Setup test fixtures."""
        self.manager = DataManager()
        
    def test_process_input(self):
        """Test input processing."""
        input_text = "example.com\ninvalid-url\ngoogle.com"
        valid_urls, invalid_urls = self.manager.process_input(input_text)
        
        assert "example.com" in valid_urls
        assert "google.com" in valid_urls
        assert "invalid-url" in invalid_urls
        
    def test_format_results(self):
        """Test result formatting."""
        results = [("example.com", 100, "Success")]
        formatted = self.manager.format_results(results)
        
        assert len(formatted) == 1
        assert formatted[0]['URL'] == "example.com"
        
    def test_export_data(self):
        """Test data export."""
        results = [("example.com", 100, "Success")]
        export_data = self.manager.export_data(results, "TSV")
        
        assert "URL\tSnapshots\tStatus" in export_data
        assert "example.com\t100\tSuccess" in export_data
