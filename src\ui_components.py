"""
UI Components Module for Wayback Machine Snapshot Checker

Contains all PyQt6 UI components and layouts for the desktop application.
"""

import sys
from typing import List, Dict, Callable, Optional
from PyQt6.QtWidgets import (
    QApplication, QMainWindow, QWidget, QVBoxLayout, QHBoxLayout,
    QTextEdit, QPushButton, QTableWidget, QTableWidgetItem, QProgressBar,
    QLabel, QStatusBar, QSplitter, QHeaderView, QMessageBox, QFrame
)
from PyQt6.QtCore import Qt, QThread, pyqtSignal, QTimer
from PyQt6.QtGui import QFont, QIcon, QClipboard

from config import (
    WINDOW_TITLE, WINDOW_WIDTH, WINDOW_HEIGHT, 
    MAX_URLS, CORPORATE_STYLE
)


class InputSection(QWidget):
    """
    Input section widget containing URL input area and control buttons.
    """
    
    def __init__(self):
        """Initialize the input section."""
        super().__init__()
        self.init_ui()
        
    def init_ui(self):
        """Initialize the user interface."""
        layout = QVBoxLayout()
        
        # Input label
        input_label = QLabel(f"Nhập danh sách URL (tối đa {MAX_URLS} URL, mỗi URL một dòng):")
        input_label.setFont(QFont("Segoe UI", 11, QFont.Weight.Medium))
        layout.addWidget(input_label)
        
        # URL input text area
        self.url_input = QTextEdit()
        self.url_input.setPlaceholderText(
            "Ví dụ:\n"
            "example.com\n"
            "https://google.com\n"
            "www.facebook.com\n"
            "github.io"
        )
        self.url_input.setMaximumHeight(200)
        layout.addWidget(self.url_input)
        
        # Button layout
        button_layout = QHBoxLayout()
        
        # Check snapshots button
        self.check_button = QPushButton("🔍 Kiểm tra Snapshots")
        self.check_button.setMinimumHeight(40)
        button_layout.addWidget(self.check_button)
        
        # Clear button
        self.clear_button = QPushButton("🗑️ Xóa")
        self.clear_button.setMinimumHeight(40)
        button_layout.addWidget(self.clear_button)
        
        # Export button
        self.export_button = QPushButton("📋 Copy to Clipboard")
        self.export_button.setMinimumHeight(40)
        self.export_button.setEnabled(False)
        button_layout.addWidget(self.export_button)
        
        layout.addLayout(button_layout)
        self.setLayout(layout)
        
    def get_input_text(self) -> str:
        """Get the current input text."""
        return self.url_input.toPlainText()
        
    def clear_input(self):
        """Clear the input text area."""
        self.url_input.clear()
        
    def set_buttons_enabled(self, enabled: bool):
        """Enable or disable control buttons."""
        self.check_button.setEnabled(enabled)
        self.clear_button.setEnabled(enabled)


class ResultsSection(QWidget):
    """
    Results section widget containing results table and progress information.
    """
    
    def __init__(self):
        """Initialize the results section."""
        super().__init__()
        self.init_ui()
        
    def init_ui(self):
        """Initialize the user interface."""
        layout = QVBoxLayout()
        
        # Results label
        results_label = QLabel("Kết quả kiểm tra:")
        results_label.setFont(QFont("Segoe UI", 11, QFont.Weight.Medium))
        layout.addWidget(results_label)
        
        # Results table
        self.results_table = QTableWidget()
        self.results_table.setColumnCount(3)
        self.results_table.setHorizontalHeaderLabels(["URL", "Snapshots", "Status"])
        
        # Configure table
        header = self.results_table.horizontalHeader()
        header.setSectionResizeMode(0, QHeaderView.ResizeMode.Stretch)
        header.setSectionResizeMode(1, QHeaderView.ResizeMode.ResizeToContents)
        header.setSectionResizeMode(2, QHeaderView.ResizeMode.ResizeToContents)
        
        self.results_table.setAlternatingRowColors(True)
        self.results_table.setSelectionBehavior(QTableWidget.SelectionBehavior.SelectRows)
        
        layout.addWidget(self.results_table)
        
        # Progress section
        progress_layout = QVBoxLayout()
        
        # Progress bar
        self.progress_bar = QProgressBar()
        self.progress_bar.setVisible(False)
        progress_layout.addWidget(self.progress_bar)
        
        # Progress label
        self.progress_label = QLabel("")
        self.progress_label.setVisible(False)
        progress_layout.addWidget(self.progress_label)
        
        layout.addLayout(progress_layout)
        self.setLayout(layout)
        
    def update_results(self, results: List[Dict[str, str]]):
        """
        Update the results table with new data.
        
        Args:
            results: List of result dictionaries
        """
        self.results_table.setRowCount(len(results))
        
        for row, result in enumerate(results):
            self.results_table.setItem(row, 0, QTableWidgetItem(result['URL']))
            self.results_table.setItem(row, 1, QTableWidgetItem(result['Snapshots']))
            
            # Color code status
            status_item = QTableWidgetItem(result['Status'])
            if result['Status'] == 'Success':
                status_item.setBackground(Qt.GlobalColor.lightGreen)
            elif result['Status'] in ['Error', 'Timeout', 'Network Error']:
                status_item.setBackground(Qt.GlobalColor.lightCoral)
            else:
                status_item.setBackground(Qt.GlobalColor.lightYellow)
                
            self.results_table.setItem(row, 2, status_item)
            
    def clear_results(self):
        """Clear the results table."""
        self.results_table.setRowCount(0)
        
    def show_progress(self, show: bool = True):
        """Show or hide progress indicators."""
        self.progress_bar.setVisible(show)
        self.progress_label.setVisible(show)
        
    def update_progress(self, value: int, current: int, total: int):
        """
        Update progress indicators.
        
        Args:
            value: Progress percentage (0-100)
            current: Current item number
            total: Total items
        """
        self.progress_bar.setValue(value)
        self.progress_label.setText(f"Đang xử lý: {current}/{total} URL")


class StatusSection(QWidget):
    """
    Status section widget showing summary information.
    """
    
    def __init__(self):
        """Initialize the status section."""
        super().__init__()
        self.init_ui()
        
    def init_ui(self):
        """Initialize the user interface."""
        layout = QHBoxLayout()
        
        # Summary labels
        self.total_label = QLabel("Tổng: 0")
        self.success_label = QLabel("Thành công: 0")
        self.error_label = QLabel("Lỗi: 0")
        self.snapshots_label = QLabel("Tổng snapshots: 0")
        
        # Style labels
        for label in [self.total_label, self.success_label, self.error_label, self.snapshots_label]:
            label.setFont(QFont("Segoe UI", 10))
            
        layout.addWidget(self.total_label)
        layout.addWidget(QLabel("|"))
        layout.addWidget(self.success_label)
        layout.addWidget(QLabel("|"))
        layout.addWidget(self.error_label)
        layout.addWidget(QLabel("|"))
        layout.addWidget(self.snapshots_label)
        layout.addStretch()
        
        self.setLayout(layout)
        
    def update_summary(self, summary: Dict[str, int]):
        """
        Update summary information.
        
        Args:
            summary: Dictionary with summary statistics
        """
        self.total_label.setText(f"Tổng: {summary.get('total_urls', 0)}")
        self.success_label.setText(f"Thành công: {summary.get('successful', 0)}")
        self.error_label.setText(f"Lỗi: {summary.get('errors', 0)}")
        self.snapshots_label.setText(f"Tổng snapshots: {summary.get('total_snapshots', 0)}")
        
    def clear_summary(self):
        """Clear summary information."""
        self.update_summary({})


class MainWindow(QMainWindow):
    """
    Main application window containing all UI components.
    """
    
    def __init__(self):
        """Initialize the main window."""
        super().__init__()
        self.init_ui()
        
    def init_ui(self):
        """Initialize the user interface."""
        self.setWindowTitle(WINDOW_TITLE)
        self.setGeometry(100, 100, WINDOW_WIDTH, WINDOW_HEIGHT)
        
        # Apply corporate styling
        self.setStyleSheet(CORPORATE_STYLE)
        
        # Central widget
        central_widget = QWidget()
        self.setCentralWidget(central_widget)
        
        # Main layout
        main_layout = QVBoxLayout()
        
        # Create sections
        self.input_section = InputSection()
        self.results_section = ResultsSection()
        self.status_section = StatusSection()
        
        # Create splitter for resizable sections
        splitter = QSplitter(Qt.Orientation.Vertical)
        splitter.addWidget(self.input_section)
        splitter.addWidget(self.results_section)
        
        # Set splitter proportions
        splitter.setSizes([250, 450])
        
        main_layout.addWidget(splitter)
        main_layout.addWidget(self.status_section)
        
        central_widget.setLayout(main_layout)
        
        # Status bar
        self.status_bar = QStatusBar()
        self.setStatusBar(self.status_bar)
        self.status_bar.showMessage("Sẵn sàng")
        
    def show_message(self, title: str, message: str, msg_type: str = "info"):
        """
        Show a message dialog.
        
        Args:
            title: Dialog title
            message: Message content
            msg_type: Message type ("info", "warning", "error")
        """
        msg_box = QMessageBox()
        msg_box.setWindowTitle(title)
        msg_box.setText(message)
        
        if msg_type == "warning":
            msg_box.setIcon(QMessageBox.Icon.Warning)
        elif msg_type == "error":
            msg_box.setIcon(QMessageBox.Icon.Critical)
        else:
            msg_box.setIcon(QMessageBox.Icon.Information)
            
        msg_box.exec()
        
    def copy_to_clipboard(self, text: str):
        """
        Copy text to system clipboard.
        
        Args:
            text: Text to copy
        """
        clipboard = QApplication.clipboard()
        clipboard.setText(text)
        self.status_bar.showMessage("Đã copy vào clipboard", 3000)
