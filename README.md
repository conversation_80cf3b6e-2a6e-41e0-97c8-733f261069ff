# Wayback Machine Snapshot Checker

Ứng dụng desktop Python hiện đại để kiểm tra số lượng snapshot đượ<PERSON> lưu trữ trên Wayback Machine cho danh sách URL.

## ✨ Tính năng chính

- **Kiểm tra hàng loạt**: Hỗ trợ kiểm tra tối đa 500 URL cùng lúc
- **Đa định dạng URL**: Hỗ trợ các định dạng `www.domain.com`, `https://domain.vn`, `domain.com`
- **Xử lý bất đồng bộ**: Xử lý đồng thời nhiều URL với hiệu suất cao
- **Giao diện hiện đại**: UI corporate-style với PyQt6
- **Xuất dữ liệu**: Copy kết quả dưới dạng TSV/CSV
- **Theo dõi tiến độ**: Progress bar và thống kê real-time
- **Xử lý lỗi**: Error handling toàn diện với timeout và retry

## 🚀 Cài đặt

### Y<PERSON><PERSON> cầu hệ thống
- Python 3.8 hoặc cao hơn
- Windows 10/11, macOS 10.14+, hoặc Linux Ubuntu 18.04+

### Cài đặt dependencies

1. **Clone repository:**
```bash
git clone <repository-url>
cd wayback-snapshot-checker
```

2. **Tạo virtual environment (khuyến nghị):**
```bash
python -m venv venv

# Windows
venv\Scripts\activate

# macOS/Linux
source venv/bin/activate
```

3. **Cài đặt dependencies:**
```bash
pip install -r requirements.txt
```

## 🎯 Sử dụng

### Chạy ứng dụng
```bash
python main.py
```

### Hướng dẫn sử dụng

1. **Nhập URL:**
   - Paste danh sách URL vào textarea (mỗi URL một dòng)
   - Hỗ trợ các định dạng: `example.com`, `https://example.com`, `www.example.com`
   - Tối đa 500 URL

2. **Kiểm tra Snapshots:**
   - Click button "🔍 Kiểm tra Snapshots"
   - Theo dõi tiến độ qua progress bar
   - Xem kết quả trong bảng

3. **Xuất kết quả:**
   - Click "📋 Copy to Clipboard" để copy kết quả
   - Định dạng TSV với header: `URL	Snapshots	Status`

4. **Xóa dữ liệu:**
   - Click "🗑️ Xóa" để clear input và kết quả

### Ví dụ output
```
URL	Snapshots	Status
example.com	1250	Success
google.com	5420	Success
invalid-url.xyz	0	Error
```

## 🏗️ Cấu trúc dự án

```
wayback-snapshot-checker/
├── main.py                 # Entry point và application controller
├── config.py              # Configuration settings
├── requirements.txt       # Python dependencies
├── README.md              # Documentation
├── src/
│   ├── __init__.py
│   ├── api_client.py      # Wayback Machine API client
│   ├── data_processor.py  # Data validation và processing
│   └── ui_components.py   # PyQt6 UI components
└── build/                 # Build scripts (optional)
```

## ⚙️ Configuration

Chỉnh sửa `config.py` để tùy chỉnh:

- `API_TIMEOUT`: Timeout cho API requests (mặc định: 10 giây)
- `REQUEST_DELAY`: Delay giữa các requests (mặc định: 0.3 giây)
- `BATCH_SIZE`: Số requests đồng thời (mặc định: 15)
- `MAX_URLS`: Số URL tối đa (mặc định: 500)

## 🔧 Development

### Chạy tests
```bash
# Cài đặt test dependencies
pip install pytest pytest-asyncio

# Chạy tests
pytest tests/
```

### Code formatting
```bash
# Cài đặt formatting tools
pip install black flake8

# Format code
black .

# Check code style
flake8 .
```

## 📦 Build Executable

### Sử dụng PyInstaller
```bash
# Cài đặt PyInstaller
pip install pyinstaller

# Build executable
pyinstaller --onefile --windowed --name="WaybackChecker" main.py
```

### Build script (Windows)
```bash
# Chạy build script
build\build_windows.bat
```

## 🐛 Troubleshooting

### Lỗi thường gặp

1. **"ModuleNotFoundError: No module named 'PyQt6'"**
   - Cài đặt lại PyQt6: `pip install PyQt6`

2. **"SSL Certificate Error"**
   - Cập nhật certificates hoặc sử dụng VPN

3. **"Too many requests"**
   - Tăng `REQUEST_DELAY` trong config.py

4. **Ứng dụng chạy chậm**
   - Giảm `BATCH_SIZE` trong config.py
   - Kiểm tra kết nối internet

### Logs
- Log file: `wayback_checker.log`
- Log level có thể thay đổi trong `main.py`

## 🤝 Contributing

1. Fork repository
2. Tạo feature branch: `git checkout -b feature/amazing-feature`
3. Commit changes: `git commit -m 'Add amazing feature'`
4. Push to branch: `git push origin feature/amazing-feature`
5. Open Pull Request

## 📄 License

Distributed under the MIT License. See `LICENSE` for more information.

## 🙏 Acknowledgments

- [Wayback Machine](https://web.archive.org/) - Internet Archive's digital archive
- [PyQt6](https://www.riverbankcomputing.com/software/pyqt/) - Cross-platform GUI toolkit
- [aiohttp](https://docs.aiohttp.org/) - Asynchronous HTTP client/server framework

## 📞 Support

Nếu gặp vấn đề hoặc có câu hỏi:
- Tạo issue trên GitHub
- Email: <EMAIL>

---

**Made with ❤️ by Senior Python Engineer**
