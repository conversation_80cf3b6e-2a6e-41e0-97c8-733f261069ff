"""
Configuration settings for Wayback Machine Snapshot Checker
"""

# API Configuration
WAYBACK_API_BASE_URL = "https://web.archive.org/cdx/search/cdx"
API_TIMEOUT = 10  # seconds
REQUEST_DELAY = 0.5  # seconds between requests to avoid rate limiting
BATCH_SIZE = 8  # number of concurrent requests

# UI Configuration
WINDOW_TITLE = "Wayback Machine Snapshot Checker"
WINDOW_WIDTH = 1000
WINDOW_HEIGHT = 700
MAX_URLS = 500

# Export Configuration
EXPORT_FORMATS = ["TSV", "CSV"]
DEFAULT_EXPORT_FORMAT = "TSV"

# Styling
CORPORATE_STYLE = """
QMainWindow {
    background-color: #f8f9fa;
    font-family: 'Segoe UI', Arial, sans-serif;
}

QTextEdit {
    border: 2px solid #e9ecef;
    border-radius: 6px;
    padding: 8px;
    font-size: 11pt;
    background-color: white;
}

QTextEdit:focus {
    border-color: #0d6efd;
}

QPushButton {
    background-color: #0d6efd;
    color: white;
    border: none;
    border-radius: 6px;
    padding: 10px 20px;
    font-size: 11pt;
    font-weight: 500;
}

QPushButton:hover {
    background-color: #0b5ed7;
}

QPushButton:pressed {
    background-color: #0a58ca;
}

QPushButton:disabled {
    background-color: #6c757d;
}

QTableWidget {
    border: 1px solid #dee2e6;
    border-radius: 6px;
    background-color: white;
    gridline-color: #e9ecef;
    font-size: 10pt;
}

QTableWidget::item {
    padding: 8px;
    border-bottom: 1px solid #e9ecef;
}

QTableWidget::item:selected {
    background-color: #e7f3ff;
}

QHeaderView::section {
    background-color: #f8f9fa;
    padding: 10px;
    border: none;
    border-bottom: 2px solid #dee2e6;
    font-weight: 600;
}

QProgressBar {
    border: 2px solid #e9ecef;
    border-radius: 6px;
    text-align: center;
    font-weight: 500;
}

QProgressBar::chunk {
    background-color: #198754;
    border-radius: 4px;
}

QLabel {
    color: #495057;
    font-size: 11pt;
}

QStatusBar {
    background-color: #f8f9fa;
    border-top: 1px solid #dee2e6;
    color: #6c757d;
}
"""
